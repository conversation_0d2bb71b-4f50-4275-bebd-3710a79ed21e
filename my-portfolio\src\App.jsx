import React, { useState, useEffect } from "react";
import "./App.css";
import { GridBackground } from "./components/ui/grid-background";
import { Timeline } from "./components/ui/timeline";

function App() {
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simulate loading time
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);
    return () => clearTimeout(timer);
  }, []);

  const experienceData = [
    {
      title: "Current",
      content: (
        <div>
          <h3 className="text-xl font-bold text-white mb-2">Full-Stack Blockchain Engineer</h3>
          <p className="text-gray-400 mb-2">Wildcard</p>
          <p className="text-gray-300 mb-4">Building smart wallet infrastructure and blockchain applications</p>
          <div className="flex flex-wrap gap-2">
            <span className="px-2 py-1 bg-blue-600 text-white text-xs rounded">Rust</span>
            <span className="px-2 py-1 bg-purple-600 text-white text-xs rounded">Solana</span>
            <span className="px-2 py-1 bg-gray-600 text-white text-xs rounded">EVM</span>
            <span className="px-2 py-1 bg-black text-white text-xs rounded">Next.js</span>
          </div>
        </div>
      ),
    },
    {
      title: "Past",
      content: (
        <div>
          <h3 className="text-xl font-bold text-white mb-2">Full-Stack Engineer</h3>
          <p className="text-gray-400 mb-2">Swifey AI</p>
          <p className="text-gray-300 mb-4">Full-stack development across web, mobile, and blockchain</p>
          <div className="flex flex-wrap gap-2">
            <span className="px-2 py-1 bg-green-600 text-white text-xs rounded">FastAPI</span>
            <span className="px-2 py-1 bg-yellow-600 text-white text-xs rounded">Node.js</span>
            <span className="px-2 py-1 bg-blue-600 text-white text-xs rounded">React</span>
            <span className="px-2 py-1 bg-blue-700 text-white text-xs rounded">TypeScript</span>
            <span className="px-2 py-1 bg-cyan-600 text-white text-xs rounded">Flutter</span>
            <span className="px-2 py-1 bg-purple-600 text-white text-xs rounded">Solana</span>
            <span className="px-2 py-1 bg-orange-600 text-white text-xs rounded">Rust</span>
          </div>
        </div>
      ),
    },
    {
      title: "2024",
      content: (
        <div>
          <h3 className="text-xl font-bold text-white mb-2">Founding Engineer</h3>
          <p className="text-gray-400 mb-2">Veritas AO</p>
          <p className="text-gray-300 mb-4">Building fair launch platform on Arweave's AO protocol</p>
          <div className="flex flex-wrap gap-2">
            <span className="px-2 py-1 bg-red-600 text-white text-xs rounded">AO</span>
            <span className="px-2 py-1 bg-blue-700 text-white text-xs rounded">TypeScript</span>
            <span className="px-2 py-1 bg-blue-600 text-white text-xs rounded">React</span>
            <span className="px-2 py-1 bg-yellow-600 text-white text-xs rounded">Node.js</span>
          </div>
        </div>
      ),
    },
    {
      title: "Internship",
      content: (
        <div>
          <h3 className="text-xl font-bold text-white mb-2">Full-Stack Engineering Intern</h3>
          <p className="text-gray-400 mb-2">Grafieks</p>
          <p className="text-gray-300 mb-4">Full-stack development internship</p>
          <div className="flex flex-wrap gap-2">
            <span className="px-2 py-1 bg-cyan-600 text-white text-xs rounded">Go</span>
            <span className="px-2 py-1 bg-yellow-600 text-white text-xs rounded">Node.js</span>
            <span className="px-2 py-1 bg-blue-600 text-white text-xs rounded">React</span>
            <span className="px-2 py-1 bg-blue-700 text-white text-xs rounded">TypeScript</span>
          </div>
        </div>
      ),
    },
  ];

  return (
    <div className="min-h-screen bg-black text-white">
      {isLoading ? (
        <div className="fixed inset-0 bg-black flex items-center justify-center">
          <div className="text-center">
            <div className="w-8 h-8 border-2 border-white border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-white">Loading...</p>
          </div>
        </div>
      ) : (
        <GridBackground className="min-h-screen">
          {/* Hero Section */}
          <div className="container mx-auto px-6 py-20">
            <div className="flex flex-col items-center text-center space-y-8">
              {/* Profile Image */}
              <div className="w-32 h-32 rounded-full overflow-hidden border-4 border-gray-700">
                <img
                  src="https://via.placeholder.com/128x128"
                  alt="Profile"
                  className="w-full h-full object-cover"
                />
              </div>

              {/* Social Links */}
              <div className="flex space-x-4">
                <a href="https://x.com/cbajpai7" className="text-gray-400 hover:text-white transition-colors">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
                  </svg>
                </a>
                <a href="https://medium.com/@cb7chaitanya" className="text-gray-400 hover:text-white transition-colors">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M13.54 12a6.8 6.8 0 01-6.77 6.82A6.8 6.8 0 010 12a6.8 6.8 0 016.77-6.82A6.8 6.8 0 0113.54 12zM20.96 12c0 3.54-1.51 6.42-3.38 6.42-1.87 0-3.39-2.88-3.39-6.42s1.52-6.42 3.39-6.42 3.38 2.88 3.38 6.42M24 12c0 3.17-.53 5.75-1.19 5.75-.66 0-1.19-2.58-1.19-5.75s.53-5.75 1.19-5.75C23.47 6.25 24 8.83 24 12z"/>
                  </svg>
                </a>
                <a href="https://github.com/cb7chaitanya" className="text-gray-400 hover:text-white transition-colors">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                  </svg>
                </a>
              </div>

              {/* Main Content */}
              <div className="max-w-4xl">
                <h1 className="text-5xl md:text-7xl font-bold mb-6">
                  Hi, I'm Chaitanya
                </h1>
                <p className="text-xl md:text-2xl text-gray-400 mb-2">
                  21, Delhi | Full Stack Engineer
                </p>
                <p className="text-lg text-gray-300 max-w-3xl mx-auto leading-relaxed">
                  I'm a Full Stack Blockchain Developer crafting cutting-edge dApps and DeFi solutions.
                  From writing secure smart contracts to building intuitive Web3 interfaces, I turn complex
                  blockchain concepts into user-friendly experiences.
                </p>
              </div>
            </div>
          </div>

          {/* Experience Section */}
          <div className="py-20">
            <div className="container mx-auto px-6">
              <h2 className="text-3xl md:text-4xl font-bold text-center mb-4">Experience</h2>
              <p className="text-gray-400 text-center mb-16 max-w-2xl mx-auto">
                Here's a timeline of my professional journey, showcasing my roles and contributions
                in blockchain and full-stack development.
              </p>
              <Timeline data={experienceData} />
            </div>
          </div>

          {/* Projects Section */}
          <div className="py-20 bg-gray-900/50">
            <div className="container mx-auto px-6">
              <h2 className="text-3xl md:text-4xl font-bold text-center mb-4">Projects</h2>
              <p className="text-gray-400 text-center mb-16 max-w-2xl mx-auto">
                A collection of my work spanning from blockchain applications to full-stack projects,
                both personal and professional.
              </p>

              {/* Project Tabs */}
              <div className="flex justify-center mb-12">
                <div className="flex bg-gray-800 rounded-lg p-1">
                  <button className="px-6 py-2 bg-blue-600 text-white rounded-md">Personal Projects</button>
                  <button className="px-6 py-2 text-gray-400 hover:text-white">Client Work</button>
                </div>
              </div>

              {/* Projects Grid */}
              <div className="grid md:grid-cols-2 gap-8 max-w-6xl mx-auto">
                {/* Project 1 */}
                <div className="bg-gray-800 rounded-lg p-6 hover:bg-gray-700 transition-colors">
                  <div className="flex justify-between items-start mb-4">
                    <h3 className="text-xl font-bold">Streamly</h3>
                    <span className="text-sm text-yellow-500 bg-yellow-500/20 px-2 py-1 rounded">Paused</span>
                  </div>
                  <p className="text-gray-300 mb-4">
                    Streamyard alternative with custom SFU server and FFMPEG stream mixing
                  </p>
                  <div className="flex flex-wrap gap-2 mb-4">
                    <span className="px-2 py-1 bg-blue-600 text-white text-xs rounded">WebRTC</span>
                    <span className="px-2 py-1 bg-green-600 text-white text-xs rounded">Mediasoup</span>
                    <span className="px-2 py-1 bg-red-600 text-white text-xs rounded">FFMPEG</span>
                    <span className="px-2 py-1 bg-yellow-600 text-white text-xs rounded">Node.js</span>
                    <span className="px-2 py-1 bg-blue-600 text-white text-xs rounded">React</span>
                  </div>
                  <a href="https://github.com/cb7chaitanya/streamly" className="text-blue-400 hover:text-blue-300">
                    View Project →
                  </a>
                </div>

                {/* Project 2 */}
                <div className="bg-gray-800 rounded-lg p-6 hover:bg-gray-700 transition-colors">
                  <h3 className="text-xl font-bold mb-4">SnipSavvy</h3>
                  <p className="text-gray-300 mb-4">
                    Code snippet management platform with multi-level sharing and organization capabilities
                  </p>
                  <div className="flex flex-wrap gap-2 mb-4">
                    <span className="px-2 py-1 bg-black text-white text-xs rounded">Next.js</span>
                    <span className="px-2 py-1 bg-blue-700 text-white text-xs rounded">TypeScript</span>
                    <span className="px-2 py-1 bg-cyan-600 text-white text-xs rounded">Tailwind</span>
                    <span className="px-2 py-1 bg-yellow-600 text-white text-xs rounded">Node.js</span>
                    <span className="px-2 py-1 bg-green-600 text-white text-xs rounded">MongoDB</span>
                  </div>
                  <a href="https://github.com/SnipSavvy/SnipSavvy_Frontend" className="text-blue-400 hover:text-blue-300">
                    View Project →
                  </a>
                </div>

                {/* Project 3 */}
                <div className="bg-gray-800 rounded-lg p-6 hover:bg-gray-700 transition-colors">
                  <h3 className="text-xl font-bold mb-4">Stream-Vault</h3>
                  <p className="text-gray-300 mb-4">
                    Decentralized video platform on WeaveVM with on-chain storage and EVM integration
                  </p>
                  <div className="flex flex-wrap gap-2 mb-4">
                    <span className="px-2 py-1 bg-orange-600 text-white text-xs rounded">Arweave</span>
                    <span className="px-2 py-1 bg-gray-600 text-white text-xs rounded">EVM</span>
                    <span className="px-2 py-1 bg-blue-600 text-white text-xs rounded">React</span>
                    <span className="px-2 py-1 bg-yellow-600 text-white text-xs rounded">Node.js</span>
                    <span className="px-2 py-1 bg-purple-600 text-white text-xs rounded">Solidity</span>
                  </div>
                  <a href="https://github.com/cb7chaitanya/StreamVault" className="text-blue-400 hover:text-blue-300">
                    View Project →
                  </a>
                </div>

                {/* Project 4 */}
                <div className="bg-gray-800 rounded-lg p-6 hover:bg-gray-700 transition-colors">
                  <h3 className="text-xl font-bold mb-4">Rust CEX Backend</h3>
                  <p className="text-gray-300 mb-4">
                    High-performance crypto exchange backend with trade matching and pub/sub system
                  </p>
                  <div className="flex flex-wrap gap-2 mb-4">
                    <span className="px-2 py-1 bg-orange-600 text-white text-xs rounded">Rust</span>
                    <span className="px-2 py-1 bg-red-600 text-white text-xs rounded">Actix Web</span>
                    <span className="px-2 py-1 bg-blue-600 text-white text-xs rounded">PostgreSQL</span>
                    <span className="px-2 py-1 bg-red-600 text-white text-xs rounded">Redis</span>
                    <span className="px-2 py-1 bg-green-600 text-white text-xs rounded">TimescaleDB</span>
                  </div>
                  <a href="https://github.com/cb7chaitanya/exchange" className="text-blue-400 hover:text-blue-300">
                    View Project →
                  </a>
                </div>
              </div>
            </div>
          </div>

          {/* Blogs Section */}
          <div className="py-20">
            <div className="container mx-auto px-6">
              <h2 className="text-3xl md:text-4xl font-bold text-center mb-4">Blogs</h2>
              <p className="text-gray-400 text-center mb-16 max-w-2xl mx-auto">
                I write about software development, sharing insights and experiences from my journey in tech.
              </p>

              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
                {/* Blog 1 */}
                <div className="bg-gray-800 rounded-lg overflow-hidden hover:bg-gray-700 transition-colors">
                  <div className="p-6">
                    <h3 className="text-xl font-bold mb-3">
                      When process.env Bites Back: A Node.js Performance Lesson
                    </h3>
                    <p className="text-gray-300 mb-4 text-sm">
                      During a past job, I was working on optimizing an internal API service. Here's what we had:
                    </p>
                    <div className="flex items-center justify-between text-sm text-gray-400">
                      <span>May 4, 2025</span>
                      <div className="flex space-x-2">
                        <span>⭐ 6</span>
                        <span>👏 6</span>
                        <span>💬 6</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Blog 2 */}
                <div className="bg-gray-800 rounded-lg overflow-hidden hover:bg-gray-700 transition-colors">
                  <div className="p-6">
                    <h3 className="text-xl font-bold mb-3">
                      Cookie-based Authentication: A Simple Guide for Secure Sessions
                    </h3>
                    <p className="text-gray-300 mb-4 text-sm">
                      What is Authentication? Authentication is the process of verifying the identity of a user, device, or entity in a system. It ensures that...
                    </p>
                    <div className="flex items-center justify-between text-sm text-gray-400">
                      <span>Jul 12, 2024</span>
                      <div className="flex space-x-2">
                        <span>⭐ 6</span>
                        <span>👏 6</span>
                        <span>💬 6</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Blog 3 */}
                <div className="bg-gray-800 rounded-lg overflow-hidden hover:bg-gray-700 transition-colors">
                  <div className="p-6">
                    <h3 className="text-xl font-bold mb-3">
                      Understanding TypeScript's Handling of Object Literal Types
                    </h3>
                    <p className="text-gray-300 mb-4 text-sm">
                      Did you know that TypeScript's handling of object literals can sometimes lead to unexpected behavior? when I started learning...
                    </p>
                    <div className="flex items-center justify-between text-sm text-gray-400">
                      <span>Jun 20, 2024</span>
                      <div className="flex space-x-2">
                        <span>⭐ 6</span>
                        <span>👏 6</span>
                        <span>💬 6</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Blog 4 */}
                <div className="bg-gray-800 rounded-lg overflow-hidden hover:bg-gray-700 transition-colors">
                  <div className="p-6">
                    <h3 className="text-xl font-bold mb-3">
                      Why Choose Next.js over React?
                    </h3>
                    <p className="text-gray-300 mb-4 text-sm">
                      In the dynamic landscape of web development, React has emerged as a powerhouse for building interactive user interfaces. However, as projects grow in complexity...
                    </p>
                    <div className="flex items-center justify-between text-sm text-gray-400">
                      <span>Apr 14, 2024</span>
                      <div className="flex space-x-2">
                        <span>⭐ 6</span>
                        <span>👏 6</span>
                        <span>💬 6</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Blog 5 */}
                <div className="bg-gray-800 rounded-lg overflow-hidden hover:bg-gray-700 transition-colors">
                  <div className="p-6">
                    <h3 className="text-xl font-bold mb-3">
                      Need of Context API (or Similar State Management Tools)
                    </h3>
                    <p className="text-gray-300 mb-4 text-sm">
                      Context API is a react structure enabling us to exchange information across all levels of a react application, think of it as a...
                    </p>
                    <div className="flex items-center justify-between text-sm text-gray-400">
                      <span>Mar 18, 2024</span>
                      <div className="flex space-x-2">
                        <span>⭐ 6</span>
                        <span>👏 6</span>
                        <span>💬 6</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Footer */}
          <footer className="py-20 border-t border-gray-800">
            <div className="container mx-auto px-6 text-center">
              <h2 className="text-2xl font-bold mb-4">Chaitanya Bajpai</h2>
              <div className="flex justify-center space-x-6 mb-8">
                <a href="https://x.com/cbajpai7" className="text-gray-400 hover:text-white transition-colors">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
                  </svg>
                </a>
                <a href="https://cal.com/chaitanya-bajpai" className="text-gray-400 hover:text-white transition-colors">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z"/>
                  </svg>
                </a>
                <a href="mailto:<EMAIL>" className="text-gray-400 hover:text-white transition-colors">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
                  </svg>
                </a>
                <a href="https://github.com/cb7chaitanya" className="text-gray-400 hover:text-white transition-colors">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                  </svg>
                </a>
              </div>
            </div>
          </footer>
        </GridBackground>
      )}
    </div>
  );
}

export default App;
