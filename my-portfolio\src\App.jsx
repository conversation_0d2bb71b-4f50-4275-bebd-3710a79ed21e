import React, { useState, useEffect } from "react";
import "./App.css";

function App() {
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simulate loading time
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 2000);
    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="app">
      {isLoading ? (
        <div className="loader">
          <div className="loader-content">
            <div className="loader-circle"></div>
            <p>Loading...</p>
          </div>
        </div>
      ) : (
        <>
          <header className="header">
            <div className="logo">CB</div>
            <nav className="nav">
              <ul>
                <li>
                  <a href="#about">About</a>
                </li>
                <li>
                  <a href="#skills">Skills</a>
                </li>
                <li>
                  <a href="#projects">Projects</a>
                </li>
                <li>
                  <a href="#contact">Contact</a>
                </li>
                <li>
                  <a href="#" className="resume-btn">
                    Resume
                  </a>
                </li>
              </ul>
            </nav>
            <div className="mobile-menu-btn">
              <span></span>
              <span></span>
              <span></span>
            </div>
          </header>

          <section className="hero">
            <div className="hero-content">
              <p className="intro-text">Hi, my name is</p>
              <h1>Chaitanya Bajpai.</h1>
              <h2>I build things for the web.</h2>
              <p className="hero-description">
                I'm a software engineer specializing in building exceptional
                digital experiences. Currently, I'm focused on building
                accessible, human-centered products.
              </p>
              <div className="hero-buttons">
                <a href="#projects" className="primary-btn">
                  Check out my work!
                </a>
              </div>
            </div>
          </section>

          <section id="about" className="about">
            <div className="section-header">
              <h2 className="section-title">
                <span>01.</span> About Me
              </h2>
              <div className="section-line"></div>
            </div>
            <div className="about-content">
              <div className="about-text">
                <p>
                  Hello! My name is Chaitanya and I enjoy creating things that
                  live on the internet. My interest in web development started
                  back in 2012 when I decided to try editing custom Tumblr
                  themes — turns out hacking together a custom reblog button
                  taught me a lot about HTML & CSS!
                </p>
                <p>
                  Fast-forward to today, and I've had the privilege of working
                  at an advertising agency, a start-up, a huge corporation, and
                  a student-led design studio. My main focus these days is
                  building accessible, inclusive products and digital
                  experiences.
                </p>
                <p>
                  Here are a few technologies I've been working with recently:
                </p>
                <ul className="skills-list">
                  <li>JavaScript (ES6+)</li>
                  <li>React</li>
                  <li>Node.js</li>
                  <li>TypeScript</li>
                  <li>Blockchain</li>
                  <li>Solidity</li>
                </ul>
              </div>
              <div className="about-image">
                <div className="image-wrapper">
                  <img
                    src="https://via.placeholder.com/300"
                    alt="Chaitanya Bajpai"
                  />
                </div>
              </div>
            </div>
          </section>

          <section id="skills" className="skills">
            <div className="section-header">
              <h2 className="section-title">
                <span>02.</span> Skills
              </h2>
              <div className="section-line"></div>
            </div>
            <div className="skills-container">
              <div className="skill-category">
                <h3>Frontend</h3>
                <div className="skill-items">
                  <div className="skill-item">React</div>
                  <div className="skill-item">JavaScript</div>
                  <div className="skill-item">TypeScript</div>
                  <div className="skill-item">HTML/CSS</div>
                  <div className="skill-item">Next.js</div>
                </div>
              </div>
              <div className="skill-category">
                <h3>Backend</h3>
                <div className="skill-items">
                  <div className="skill-item">Node.js</div>
                  <div className="skill-item">Express</div>
                  <div className="skill-item">MongoDB</div>
                  <div className="skill-item">PostgreSQL</div>
                </div>
              </div>
              <div className="skill-category">
                <h3>Blockchain</h3>
                <div className="skill-items">
                  <div className="skill-item">Solidity</div>
                  <div className="skill-item">Ethereum</div>
                  <div className="skill-item">Web3.js</div>
                  <div className="skill-item">Smart Contracts</div>
                </div>
              </div>
            </div>
          </section>

          <section id="projects" className="projects">
            <div className="section-header">
              <h2 className="section-title">
                <span>03.</span> Some Things I've Built
              </h2>
              <div className="section-line"></div>
            </div>
            <div className="projects-grid">
              <div className="project-card">
                <div className="project-content">
                  <p className="project-overline">Featured Project</p>
                  <h3 className="project-title">DeFi Exchange Platform</h3>
                  <div className="project-description">
                    <p>
                      A decentralized exchange platform that allows users to
                      swap tokens, provide liquidity, and earn rewards. Built
                      with React, Solidity, and Web3.js.
                    </p>
                  </div>
                  <ul className="project-tech-list">
                    <li>React</li>
                    <li>Solidity</li>
                    <li>Web3.js</li>
                    <li>Ethereum</li>
                  </ul>
                  <div className="project-links">
                    <a href="#" aria-label="GitHub Link">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        role="img"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="feather feather-github"
                      >
                        <path d="M9 19c-5 1.5-5-2.5-7-3m14 6v-3.87a3.37 3.37 0 0 0-.94-2.61c3.14-.35 6.44-1.54 6.44-7A5.44 5.44 0 0 0 20 4.77 5.07 5.07 0 0 0 19.91 1S18.73.65 16 2.48a13.38 13.38 0 0 0-7 0C6.27.65 5.09 1 5.09 1A5.07 5.07 0 0 0 5 4.77a5.44 5.44 0 0 0-1.5 3.78c0 5.42 3.3 6.61 6.44 7A3.37 3.37 0 0 0 9 18.13V22"></path>
                      </svg>
                    </a>
                    <a href="#" aria-label="External Link">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        role="img"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="feather feather-external-link"
                      >
                        <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
                        <polyline points="15 3 21 3 21 9"></polyline>
                        <line x1="10" y1="14" x2="21" y2="3"></line>
                      </svg>
                    </a>
                  </div>
                </div>
                <div className="project-image">
                  <img
                    src="https://via.placeholder.com/500x300"
                    alt="Project screenshot"
                  />
                </div>
              </div>

              <div className="project-card reverse">
                <div className="project-content">
                  <p className="project-overline">Featured Project</p>
                  <h3 className="project-title">NFT Marketplace</h3>
                  <div className="project-description">
                    <p>
                      A marketplace for creating, buying, and selling NFTs with
                      support for multiple blockchain networks.
                    </p>
                  </div>
                  <ul className="project-tech-list">
                    <li>React</li>
                    <li>Next.js</li>
                    <li>ERC-721</li>
                    <li>IPFS</li>
                  </ul>
                  <div className="project-links">
                    <a href="#" aria-label="GitHub Link">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        role="img"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="feather feather-github"
                      >
                        <path d="M9 19c-5 1.5-5-2.5-7-3m14 6v-3.87a3.37 3.37 0 0 0-.94-2.61c3.14-.35 6.44-1.54 6.44-7A5.44 5.44 0 0 0 20 4.77 5.07 5.07 0 0 0 19.91 1S18.73.65 16 2.48a13.38 13.38 0 0 0-7 0C6.27.65 5.09 1 5.09 1A5.07 5.07 0 0 0 5 4.77a5.44 5.44 0 0 0-1.5 3.78c0 5.42 3.3 6.61 6.44 7A3.37 3.37 0 0 0 9 18.13V22"></path>
                      </svg>
                    </a>
                    <a href="#" aria-label="External Link">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        role="img"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="feather feather-external-link"
                      >
                        <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
                        <polyline points="15 3 21 3 21 9"></polyline>
                        <line x1="10" y1="14" x2="21" y2="3"></line>
                      </svg>
                    </a>
                  </div>
                </div>
                <div className="project-image">
                  <img
                    src="https://via.placeholder.com/500x300"
                    alt="Project screenshot"
                  />
                </div>
              </div>
            </div>
          </section>

          <section id="contact" className="contact">
            <div className="section-header">
              <h2 className="section-title">
                <span>04.</span> What's Next?
              </h2>
              <div className="section-line"></div>
            </div>
            <div className="contact-content">
              <h2>Get In Touch</h2>
              <p>
                I'm currently looking for new opportunities. Whether you have a
                question or just want to say hi, I'll try my best to get back to
                you!
              </p>
              <a href="mailto:<EMAIL>" className="contact-btn">
                Say Hello
              </a>
            </div>
          </section>

          <footer className="footer">
            <div className="social-links">
              <a href="#" aria-label="GitHub">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  role="img"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="feather feather-github"
                >
                  <path d="M9 19c-5 1.5-5-2.5-7-3m14 6v-3.87a3.37 3.37 0 0 0-.94-2.61c3.14-.35 6.44-1.54 6.44-7A5.44 5.44 0 0 0 20 4.77 5.07 5.07 0 0 0 19.91 1S18.73.65 16 2.48a13.38 13.38 0 0 0-7 0C6.27.65 5.09 1 5.09 1A5.07 5.07 0 0 0 5 4.77a5.44 5.44 0 0 0-1.5 3.78c0 5.42 3.3 6.61 6.44 7A3.37 3.37 0 0 0 9 18.13V22"></path>
                </svg>
              </a>
              <a href="#" aria-label="LinkedIn">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  role="img"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="feather feather-linkedin"
                >
                  <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"></path>
                  <rect x="2" y="9" width="4" height="12"></rect>
                  <circle cx="4" cy="4" r="2"></circle>
                </svg>
              </a>
              <a href="#" aria-label="Twitter">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  role="img"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="feather feather-twitter"
                >
                  <path d="M23 3a10.9 10.9 0 0 1-3.14 1.53 4.48 4.48 0 0 0-7.86 3v1A10.66 10.66 0 0 1 3 4s-4 9 5 13a11.64 11.64 0 0 1-7 2c9 5 20 0 20-11.5a4.5 4.5 0 0 0-.08-.83A7.72 7.72 0 0 0 23 3z"></path>
                </svg>
              </a>
            </div>
            <div className="footer-text">
              <p>Designed & Built by Chaitanya Bajpai</p>
              <p>© {new Date().getFullYear()} All Rights Reserved</p>
            </div>
          </footer>
        </>
      )}
    </div>
  );
}

export default App;
