import { MotionValue } from '../../../../../motion-dom/dist/es/value/index.mjs';
import { transformProps } from '../../../../../motion-dom/dist/es/render/utils/keys-transform.mjs';
import { acceleratedValues } from '../../../../../motion-dom/dist/es/animation/waapi/utils/accelerated-values.mjs';

class WillChangeMotionValue extends MotionValue {
    constructor() {
        super(...arguments);
        this.isEnabled = false;
    }
    add(name) {
        if (transformProps.has(name) || acceleratedValues.has(name)) {
            this.isEnabled = true;
            this.update();
        }
    }
    update() {
        this.set(this.isEnabled ? "transform" : "auto");
    }
}

export { WillChangeMotionValue };
