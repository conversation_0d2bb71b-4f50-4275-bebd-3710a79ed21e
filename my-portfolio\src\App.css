/* Base styles */
:root {
  --navy: #0a192f;
  --light-navy: #112240;
  --lightest-navy: #233554;
  --slate: #8892b0;
  --light-slate: #a8b2d1;
  --lightest-slate: #ccd6f6;
  --white: #e6f1ff;
  --green: #64ffda;
  --green-tint: rgba(100, 255, 218, 0.1);
  --font-sans: 'Calibre', 'Inter', 'San Francisco', 'SF Pro Text', -apple-system, system-ui, sans-serif;
  --font-mono: 'SF Mono', 'Fira Code', 'Fira Mono', 'Roboto Mono', monospace;
  --fz-xxs: 12px;
  --fz-xs: 13px;
  --fz-sm: 14px;
  --fz-md: 16px;
  --fz-lg: 18px;
  --fz-xl: 20px;
  --fz-xxl: 22px;
  --fz-heading: 32px;
  --border-radius: 4px;
  --nav-height: 100px;
  --nav-scroll-height: 70px;
  --tab-height: 42px;
  --tab-width: 120px;
  --easing: cubic-bezier(0.645, 0.045, 0.355, 1);
  --transition: all 0.25s cubic-bezier(0.645, 0.045, 0.355, 1);
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
}

body {
  margin: 0;
  width: 100%;
  min-height: 100%;
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  background-color: var(--navy);
  color: var(--slate);
  font-family: var(--font-sans);
  font-size: var(--fz-xl);
  line-height: 1.3;
}

section {
  margin: 0 auto;
  padding: 100px 0;
  max-width: 1000px;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0 0 10px 0;
  font-weight: 600;
  color: var(--lightest-slate);
  line-height: 1.1;
}

a {
  display: inline-block;
  text-decoration: none;
  text-decoration-skip-ink: auto;
  color: inherit;
  position: relative;
  transition: var(--transition);
}

a:hover,
a:focus {
  color: var(--green);
}

/* Loader */
.loader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--navy);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 99;
}

.loader-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loader-circle {
  width: 50px;
  height: 50px;
  border: 3px solid var(--lightest-navy);
  border-top: 3px solid var(--green);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* Header */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: fixed;
  top: 0;
  z-index: 11;
  padding: 0px 50px;
  width: 100%;
  height: var(--nav-height);
  background-color: rgba(10, 25, 47, 0.85);
  backdrop-filter: blur(10px);
  transition: var(--transition);
}

.logo {
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--green);
  font-family: var(--font-mono);
  font-size: 24px;
  font-weight: 700;
}

.nav ul {
  display: flex;
  justify-content: space-between;
  align-items: center;
  list-style: none;
  padding: 0;
  margin: 0;
}

.nav li {
  margin: 0 15px;
}

.nav a {
  padding: 10px;
  font-family: var(--font-mono);
  font-size: var(--fz-xs);
  color: var(--lightest-slate);
}

.nav a:hover {
  color: var(--green);
}

.resume-btn {
  color: var(--green);
  background-color: transparent;
  border: 1px solid var(--green);
  border-radius: var(--border-radius);
  padding: 0.75rem 1rem;
  font-family: var(--font-mono);
  line-height: 1;
  text-decoration: none;
  cursor: pointer;
  transition: var(--transition);
}

.resume-btn:hover {
  background-color: var(--green-tint);
}

.mobile-menu-btn {
  display: none;
  flex-direction: column;
  justify-content: space-around;
  width: 30px;
  height: 25px;
  background: transparent;
  border: none;
  cursor: pointer;
  z-index: 10;
}

.mobile-menu-btn span {
  width: 30px;
  height: 3px;
  background: var(--green);
  border-radius: 5px;
  transition: all 0.3s linear;
}

@media (max-width: 768px) {
  .mobile-menu-btn {
    display: flex;
  }

  .nav {
    display: none;
  }
}

/* Hero Section */
.hero {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  flex-direction: column;
  min-height: 100vh;
  padding: 0;
}

.hero-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.intro-text {
  margin: 0 0 30px 4px;
  color: var(--green);
  font-family: var(--font-mono);
  font-size: var(--fz-md);
  font-weight: 400;
}

.hero h1 {
  margin: 0;
  font-size: clamp(40px, 8vw, 80px);
  font-weight: 600;
  color: var(--lightest-slate);
}

.hero h2 {
  margin-top: 5px;
  color: var(--slate);
  font-size: clamp(40px, 8vw, 80px);
  font-weight: 600;
  line-height: 0.9;
}

.hero-description {
  margin: 20px 0 0;
  max-width: 540px;
  color: var(--slate);
}

.hero-buttons {
  margin-top: 50px;
}

.primary-btn {
  color: var(--green);
  background-color: transparent;
  border: 1px solid var(--green);
  border-radius: var(--border-radius);
  padding: 1.25rem 1.75rem;
  font-size: var(--fz-sm);
  font-family: var(--font-mono);
  line-height: 1;
  text-decoration: none;
  cursor: pointer;
  transition: var(--transition);
}

.primary-btn:hover {
  background-color: var(--green-tint);
}

/* Section Styling */
.section-header {
  display: flex;
  align-items: center;
  position: relative;
  margin: 10px 0 40px;
  width: 100%;
  font-size: clamp(26px, 5vw, var(--fz-heading));
  white-space: nowrap;
}

.section-title {
  display: flex;
  align-items: center;
  position: relative;
  margin: 10px 0 40px;
  width: 100%;
  font-size: clamp(26px, 5vw, var(--fz-heading));
  white-space: nowrap;
  color: var(--lightest-slate);
}

.section-title span {
  color: var(--green);
  margin-right: 10px;
  font-family: var(--font-mono);
  font-size: clamp(var(--fz-md), 3vw, var(--fz-xl));
  font-weight: 400;
}

.section-line {
  flex-grow: 1;
  height: 1px;
  margin-left: 20px;
  background-color: var(--lightest-navy);
}

/* About Section */
.about-content {
  display: grid;
  grid-template-columns: 3fr 2fr;
  gap: 50px;
}

.about-text {
  color: var(--slate);
}

.about-text p {
  margin-bottom: 15px;
}

.skills-list {
  display: grid;
  grid-template-columns: repeat(2, minmax(140px, 200px));
  gap: 0 10px;
  padding: 0;
  margin: 20px 0 0 0;
  overflow: hidden;
  list-style: none;
}

.skills-list li {
  position: relative;
  margin-bottom: 10px;
  padding-left: 20px;
  font-family: var(--font-mono);
  font-size: var(--fz-xs);
}

.skills-list li::before {
  content: '▹';
  position: absolute;
  left: 0;
  color: var(--green);
  font-size: var(--fz-sm);
  line-height: 12px;
}

.about-image {
  position: relative;
  max-width: 300px;
}

.image-wrapper {
  display: block;
  position: relative;
  width: 100%;
  border-radius: var(--border-radius);
  background-color: var(--green);
}

.image-wrapper::before {
  content: '';
  display: block;
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: var(--border-radius);
  transition: var(--transition);
  top: 20px;
  left: 20px;
  z-index: -1;
  border: 2px solid var(--green);
}

.image-wrapper img {
  position: relative;
  border-radius: var(--border-radius);
  mix-blend-mode: multiply;
  filter: grayscale(100%) contrast(1);
  transition: var(--transition);
  width: 100%;
}

.image-wrapper:hover::before {
  top: 15px;
  left: 15px;
}

.image-wrapper:hover img {
  filter: none;
  mix-blend-mode: normal;
}

@media (max-width: 768px) {
  .about-content {
    display: block;
  }

  .about-image {
    margin: 50px auto 0;
  }
}

/* Skills Section */
.skills-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 30px;
}

.skill-category {
  background-color: var(--light-navy);
  border-radius: var(--border-radius);
  padding: 25px;
  transition: var(--transition);
}

.skill-category:hover {
  transform: translateY(-5px);
}

.skill-category h3 {
  color: var(--green);
  font-family: var(--font-mono);
  font-size: var(--fz-lg);
  margin-bottom: 20px;
}

.skill-items {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}

.skill-item {
  background-color: var(--lightest-navy);
  color: var(--light-slate);
  padding: 10px;
  border-radius: var(--border-radius);
  text-align: center;
  font-size: var(--fz-sm);
  transition: var(--transition);
}

.skill-item:hover {
  color: var(--green);
  transform: translateY(-2px);
}

/* Projects Section */
.projects-grid {
  display: flex;
  flex-direction: column;
  gap: 100px;
}

.project-card {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  align-items: center;
}

.project-content {
  position: relative;
  grid-area: 1 / 1 / -1 / 7;
  grid-column: 1 / 8;
  z-index: 2;
}

.project-image {
  grid-column: 6 / -1;
  grid-row: 1 / -1;
  position: relative;
  z-index: 1;
}

.project-card.reverse .project-content {
  grid-column: 6 / -1;
  text-align: right;
}

.project-card.reverse .project-image {
  grid-column: 1 / 8;
}

.project-overline {
  margin: 10px 0;
  color: var(--green);
  font-family: var(--font-mono);
  font-size: var(--fz-xs);
  font-weight: 400;
}

.project-title {
  color: var(--lightest-slate);
  font-size: clamp(24px, 5vw, 28px);
  margin-bottom: 20px;
}

.project-description {
  position: relative;
  z-index: 2;
  padding: 25px;
  border-radius: var(--border-radius);
  background-color: var(--light-navy);
  color: var(--light-slate);
  font-size: var(--fz-lg);
  box-shadow: 0 10px 30px -15px rgba(2, 12, 27, 0.7);
}

.project-tech-list {
  display: flex;
  flex-wrap: wrap;
  position: relative;
  z-index: 2;
  margin: 25px 0 10px;
  padding: 0;
  list-style: none;
}

.project-card.reverse .project-tech-list {
  justify-content: flex-end;
}

.project-tech-list li {
  margin: 0 20px 5px 0;
  color: var(--light-slate);
  font-